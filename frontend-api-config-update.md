# Frontend API Configuration Update

## Update Required Files

### 1. Environment Variables (.env.production)

Create or update `.env.production` in your frontend root:

```env
# Production API Configuration
VITE_MPESA_API_URL=https://api.blockcoopsacco.com/api
VITE_API_BASE_URL=https://api.blockcoopsacco.com/api

# Blockchain Configuration (if needed)
VITE_BLOCKCHAIN_RPC_URL=https://bsc-dataseed1.binance.org/
VITE_CHAIN_ID=56
```

### 2. Update src/services/mpesaApi.ts

Replace the API_BASE_URL line:

```typescript
// Change from:
const API_BASE_URL = import.meta.env.VITE_MPESA_API_URL || 'http://localhost:3001/api';

// To:
const API_BASE_URL = import.meta.env.VITE_MPESA_API_URL || 'https://api.blockcoopsacco.com/api';
```

### 3. Update any other API service files

If you have other API service files, update them similarly:

```typescript
// In any API service file
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://api.blockcoopsacco.com/api';
```

### 4. Build and Deploy Frontend

After updating the configuration:

```bash
# Build the frontend with production environment
npm run build

# Upload the new dist folder to your cPanel
# Replace the contents of /public_html/shares/
```

## Testing the Connection

### 1. Test API Health
Visit: https://api.blockcoopsacco.com/health

Should return:
```json
{
  "status": "OK",
  "timestamp": "2024-01-XX...",
  "uptime": 123.45,
  "environment": "production"
}
```

### 2. Test from Frontend
Open browser console on your live site and run:

```javascript
fetch('https://api.blockcoopsacco.com/api/status')
  .then(response => response.json())
  .then(data => console.log('API Status:', data))
  .catch(error => console.error('API Error:', error));
```

### 3. Test M-Pesa Integration
Try initiating a small test payment through your frontend to verify the complete flow works.

## Troubleshooting

### CORS Issues
If you get CORS errors:
1. Check that your frontend domain is in the backend CORS_ORIGIN
2. Verify SSL certificates are working
3. Check browser console for specific CORS error messages

### API Connection Issues
1. Verify the backend is running in cPanel Node.js
2. Check the domain/subdomain configuration
3. Test the health endpoint directly
4. Review backend logs in cPanel

### M-Pesa Issues
1. Verify M-Pesa credentials are correctly set in production
2. Check callback URLs are accessible
3. Test with small amounts first
4. Monitor backend logs for M-Pesa API responses
