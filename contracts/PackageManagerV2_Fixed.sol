// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

// Interfaces for external contracts
interface IERC20Decimals is IERC20 {
    function decimals() external view returns (uint8);
}

interface IBLOCKS is IERC20 {
    function mint(address to, uint256 amount) external;
}

interface IBLOCKS_LP is IERC20 {
    function mint(address to, uint256 amount) external;
}

interface IVestingVault {
    function lock(address user, uint256 amount, uint64 cliff, uint64 duration) external;
}

interface IPancakeRouter {
    function addLiquidity(
        address tokenA, address tokenB, uint256 amountADesired, uint256 amountBDesired,
        uint256 amountAMin, uint256 amountBMin, address to, uint256 deadline
    ) external returns (uint256 amountA, uint256 amountB, uint256 liquidity);
    
    function factory() external pure returns (address);
}

interface IPancakeFactory {
    function getPair(address tokenA, address tokenB) external view returns (address pair);
}

interface ISwapTaxManager {
    function buckets(bytes32 key) external view returns (uint16 rateBps, address recipient);
}

/**
 * @title PackageManagerV2_Fixed
 * @dev Fixed version of PackageManager with corrected business logic:
 *      - Referral rewards paid from Treasury (not minted)
 *      - 70/30 USDT split (70% treasury, 30% liquidity)
 *      - Global target price affects only LP allocation
 *      - 1:1 BLOCKS-LP token distribution
 */
contract PackageManagerV2_Fixed is AccessControl, ReentrancyGuard, Pausable {
    using SafeERC20 for IERC20;
    using SafeERC20 for IERC20Decimals;

    bytes32 public constant ADMIN_ROLE = DEFAULT_ADMIN_ROLE;
    bytes32 public constant PACKAGE_MANAGER_ROLE = keccak256("PACKAGE_MANAGER_ROLE");

    // Tax bucket keys for SwapTaxManager integration
    bytes32 public constant PURCHASE_TAX_KEY = keccak256("PURCHASE");
    bytes32 public constant REFERRAL_TAX_KEY = keccak256("REFERRAL");

    struct Package {
        string   name;
        uint256  entryUSDT;
        uint256  exchangeRate;     // Exchange rate: USDT per BLOCKS for user token allocation
        uint16   vestBps;          // Percentage of user tokens to vest (basis points)
        uint64   cliff;            // Cliff period in seconds
        uint64   duration;         // Total vesting duration in seconds
        uint16   referralBps;      // Referral reward percentage (basis points)
        bool     active;
        bool     exists;
    }

    struct UserPurchase {
        uint256 packageId;
        uint256 usdtAmount;
        uint256 totalTokens;
        uint256 vestTokens;
        uint256 poolTokens;
        uint256 lpTokens;
        address referrer;
        uint256 referralReward;
        uint256 timestamp;
    }

    struct UserStats {
        uint256 totalInvested;
        uint256 totalTokensReceived;
        uint256 totalVestTokens;
        uint256 totalPoolTokens;
        uint256 totalLPTokens;
        uint256 totalReferralRewards;
        uint256 purchaseCount;
    }

    struct ReferralEarning {
        address buyer;
        uint256 packageId;
        uint256 reward;
        uint256 timestamp;
        uint256 blockNumber;
    }

    // State variables
    IERC20Decimals   public immutable usdt;
    IBLOCKS          public immutable shareToken;
    IBLOCKS_LP       public immutable lpToken;
    IVestingVault    public immutable vestingVault;
    IPancakeRouter   public immutable router;
    IPancakeFactory  public immutable factory;
    ISwapTaxManager  public immutable taxManager;
    address          public           treasury;
    uint256          public           globalTargetPrice; // Global target price for liquidity operations only
    uint256          public           slippageToleranceBps = 500; // 5% slippage tolerance
    uint256          public           treasuryAllocationBps = 7000; // 70% to treasury
    uint256          public           treasuryMinimumThreshold = 1000 * 1e18; // Minimum BLOCKS in treasury

    uint256   public nextPackageId;
    uint256[] private _packageIds;
    mapping(uint256 => Package) private _packages;
    mapping(address => UserStats) private _userStats;
    mapping(address => UserPurchase[]) private _userPurchases;
    mapping(address => mapping(uint256 => bool)) private _userPackagePurchased;
    mapping(address => uint256) public pendingReferralRewards; // Track pending referral rewards
    mapping(address => ReferralEarning[]) private _referralEarnings;

    // Events
    event PackageAdded(
        uint256 indexed id, string name, uint256 entryUSDT, uint256 exchangeRate,
        uint16 vestBps, uint64 cliff, uint64 duration, uint16 referralBps
    );
    
    event PackagePurchased(
        address indexed user, uint256 indexed packageId, uint256 usdtAmount,
        uint256 totalTokens, uint256 vestTokens, uint256 poolTokens, uint256 lpTokens,
        address referrer, uint256 referralReward
    );
    
    event LiquidityAdded(
        address indexed user, uint256 indexed packageId,
        uint256 usdtAmount, uint256 blocksAmount, uint256 liquidity
    );
    
    event ReferralRewardPaid(
        address indexed referrer, address indexed buyer,
        uint256 indexed packageId, uint256 reward
    );
    
    event ReferralPaymentSkipped(
        address indexed referrer, address indexed buyer, uint256 indexed packageId,
        uint256 rewardAmount, uint256 treasuryBalance
    );
    
    event PendingReferralAdded(
        address indexed referrer, uint256 amount, uint256 totalPending
    );
    
    event GlobalTargetPriceUpdated(uint256 oldPrice, uint256 newPrice);
    event TreasuryThresholdUpdated(uint256 oldThreshold, uint256 newThreshold);
    event TreasuryLowBalance(uint256 currentBalance, uint256 threshold);
    event InsufficientTreasuryFunds(uint256 required, uint256 available);

    modifier onlyAdmin() {
        require(hasRole(ADMIN_ROLE, msg.sender), "PackageManager: Admin role required");
        _;
    }

    modifier validPackage(uint256 id) {
        require(_packages[id].exists, "PackageManager: Package does not exist");
        _;
    }

    /**
     * @dev Constructor
     * @param usdt_ USDT token contract address (must be 18 decimals)
     * @param shareToken_ BLOCKS token contract address
     * @param lpToken_ BLOCKS-LP token contract address
     * @param vestingVault_ Vesting vault contract address
     * @param router_ PancakeSwap router contract address
     * @param factory_ PancakeSwap factory contract address
     * @param treasury_ Treasury address for receiving funds
     * @param taxManager_ Tax manager contract address
     * @param admin Admin address for access control
     * @param initialGlobalTargetPrice_ Initial global target price for liquidity operations (18 decimals)
     */
    constructor(
        address usdt_,
        address shareToken_,
        address lpToken_,
        address vestingVault_,
        address router_,
        address factory_,
        address treasury_,
        address taxManager_,
        address admin,
        uint256 initialGlobalTargetPrice_
    ) {
        require(usdt_ != address(0), "Invalid USDT address");
        require(shareToken_ != address(0), "Invalid share token address");
        require(lpToken_ != address(0), "Invalid LP token address");
        require(vestingVault_ != address(0), "Invalid vault address");
        require(router_ != address(0), "Invalid router address");
        require(factory_ != address(0), "Invalid factory address");
        require(treasury_ != address(0), "Invalid treasury address");
        require(taxManager_ != address(0), "Invalid tax manager address");
        require(admin != address(0), "Invalid admin address");
        require(initialGlobalTargetPrice_ > 0, "Invalid initial global target price");

        usdt         = IERC20Decimals(usdt_);
        shareToken   = IBLOCKS(shareToken_);
        lpToken      = IBLOCKS_LP(lpToken_);
        vestingVault = IVestingVault(vestingVault_);
        router       = IPancakeRouter(router_);
        factory      = IPancakeFactory(factory_);
        taxManager   = ISwapTaxManager(taxManager_);
        treasury     = treasury_;
        globalTargetPrice = initialGlobalTargetPrice_;
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(PACKAGE_MANAGER_ROLE, admin);
    }

    /**
     * @dev Add a new investment package
     * @param name Package name
     * @param entryUSDT Entry amount in USDT (18 decimals)
     * @param exchangeRate Exchange rate: USDT per BLOCKS for user token allocation (18 decimals)
     * @param vestBps Percentage of user tokens to vest in basis points
     * @param cliff Cliff period in seconds before vesting starts
     * @param duration Total vesting duration in seconds
     * @param referralBps Referral reward in basis points (max 1000 = 10%)
     */
    function addPackage(
        string memory name,
        uint256 entryUSDT,
        uint256 exchangeRate,
        uint16 vestBps,
        uint64 cliff,
        uint64 duration,
        uint16 referralBps
    ) external onlyRole(PACKAGE_MANAGER_ROLE) {
        require(bytes(name).length > 0, "PackageManager: Empty name");
        require(entryUSDT > 0, "PackageManager: Invalid entry amount");
        require(exchangeRate > 0, "PackageManager: Invalid exchange rate");
        require(vestBps <= 10000, "PackageManager: Invalid vest percentage");
        require(referralBps <= 1000, "PackageManager: Referral rate too high"); // Max 10%
        require(duration > 0, "PackageManager: Invalid duration");

        uint256 id = nextPackageId++;
        _packages[id] = Package({
            name: name,
            entryUSDT: entryUSDT,
            exchangeRate: exchangeRate,
            vestBps: vestBps,
            cliff: cliff,
            duration: duration,
            referralBps: referralBps,
            active: true,
            exists: true
        });
        _packageIds.push(id);

        emit PackageAdded(id, name, entryUSDT, exchangeRate, vestBps, cliff, duration, referralBps);
    }

    /**
     * @dev Set global target price for liquidity operations
     * @param newPrice New global target price in wei precision (18 decimals)
     */
    function setGlobalTargetPrice(uint256 newPrice) external onlyAdmin {
        require(newPrice > 0, "PackageManager: Invalid global target price");
        uint256 oldPrice = globalTargetPrice;
        globalTargetPrice = newPrice;
        emit GlobalTargetPriceUpdated(oldPrice, newPrice);
    }

    /**
     * @dev Set treasury minimum threshold for referral payments
     * @param newThreshold New minimum threshold in BLOCKS tokens (18 decimals)
     */
    function setTreasuryMinimumThreshold(uint256 newThreshold) external onlyAdmin {
        uint256 oldThreshold = treasuryMinimumThreshold;
        treasuryMinimumThreshold = newThreshold;
        emit TreasuryThresholdUpdated(oldThreshold, newThreshold);
    }

    /**
     * @dev Purchase a package with corrected business logic
     * @param id Package ID to purchase
     * @param referrer Address of referrer (can be zero address)
     */
    function purchase(uint256 id, address referrer) external nonReentrant whenNotPaused validPackage(id) {
        Package storage pkg = _packages[id];
        require(pkg.active, "PackageManager: Package not active");
        require(!_userPackagePurchased[msg.sender][id], "PackageManager: Package already purchased");

        // Verify USDT decimals for V2 architecture
        uint8 usdtDecimals = usdt.decimals();
        require(usdtDecimals == 18, "PackageManager: USDT must use 18 decimals for V2 architecture");

        // Transfer USDT from user
        usdt.safeTransferFrom(msg.sender, address(this), pkg.entryUSDT);

        // Calculate user tokens based on package exchange rate
        uint256 totalUserTokens = (pkg.entryUSDT * 1e18) / pkg.exchangeRate;

        // FIXED: 70/30 USDT split (70% treasury, 30% liquidity) - NOT based on vestBps
        uint256 usdtForTreasury = (pkg.entryUSDT * treasuryAllocationBps) / 10_000; // 70%
        uint256 usdtForPool = pkg.entryUSDT - usdtForTreasury; // 30%

        // Calculate vesting and immediate token allocation based on vestBps
        uint256 vestTokens = (totalUserTokens * pkg.vestBps) / 10_000;
        uint256 immediateTokens = totalUserTokens - vestTokens;

        // Handle vesting tokens
        if (vestTokens > 0) {
            shareToken.mint(address(vestingVault), vestTokens);
            vestingVault.lock(msg.sender, vestTokens, pkg.cliff, pkg.duration);
        }

        // Transfer treasury portion to treasury
        if (usdtForTreasury > 0) {
            usdt.safeTransfer(treasury, usdtForTreasury);
        }

        // Handle liquidity provision using global target price
        uint256 lpTokens = 0;
        if (usdtForPool > 0 && globalTargetPrice > 0) {
            // Calculate BLOCKS tokens for liquidity based on global target price
            uint256 liquidityBLOCKS = (usdtForPool * 1e18) / globalTargetPrice;

            // Mint BLOCKS tokens for liquidity
            shareToken.mint(address(this), liquidityBLOCKS);

            // Add liquidity to DEX
            uint256 liquidity = _addLiquidity(usdtForPool, liquidityBLOCKS);

            // FIXED: 1:1 BLOCKS-LP token distribution (equal to BLOCKS tokens received)
            lpTokens = liquidityBLOCKS; // 1:1 ratio
            lpToken.mint(msg.sender, lpTokens);

            emit LiquidityAdded(msg.sender, id, usdtForPool, liquidityBLOCKS, liquidity);
        }

        // FIXED: Handle referral rewards from Treasury (not minting)
        uint256 referralReward = 0;
        if (referrer != address(0) && pkg.referralBps > 0) {
            referralReward = (totalUserTokens * pkg.referralBps) / 10_000;

            // Check treasury balance
            uint256 treasuryBalance = IERC20(address(shareToken)).balanceOf(treasury);

            if (treasuryBalance >= referralReward && treasuryBalance >= treasuryMinimumThreshold) {
                // Pay referral reward from treasury
                // Note: Treasury must have approved this contract to spend BLOCKS tokens
                IERC20(address(shareToken)).transferFrom(treasury, referrer, referralReward);

                // Update referrer's stats
                _userStats[referrer].totalReferralRewards += referralReward;

                // Record referral earning
                _referralEarnings[referrer].push(ReferralEarning({
                    buyer: msg.sender,
                    packageId: id,
                    reward: referralReward,
                    timestamp: block.timestamp,
                    blockNumber: block.number
                }));

                emit ReferralRewardPaid(referrer, msg.sender, id, referralReward);
            } else {
                // Add to pending referrals
                pendingReferralRewards[referrer] += referralReward;
                emit PendingReferralAdded(referrer, referralReward, pendingReferralRewards[referrer]);
                emit ReferralPaymentSkipped(referrer, msg.sender, id, referralReward, treasuryBalance);

                // Emit low balance warning
                if (treasuryBalance < treasuryMinimumThreshold) {
                    emit TreasuryLowBalance(treasuryBalance, treasuryMinimumThreshold);
                }
            }
        }

        // Update user stats
        _userStats[msg.sender].totalInvested += pkg.entryUSDT;
        _userStats[msg.sender].totalTokensReceived += totalUserTokens;
        _userStats[msg.sender].totalVestTokens += vestTokens;
        _userStats[msg.sender].totalPoolTokens += immediateTokens;
        _userStats[msg.sender].totalLPTokens += lpTokens;
        _userStats[msg.sender].purchaseCount++;

        // Record purchase
        _userPurchases[msg.sender].push(UserPurchase({
            packageId: id,
            usdtAmount: pkg.entryUSDT,
            totalTokens: totalUserTokens,
            vestTokens: vestTokens,
            poolTokens: immediateTokens,
            lpTokens: lpTokens,
            referrer: referrer,
            referralReward: referralReward,
            timestamp: block.timestamp
        }));

        _userPackagePurchased[msg.sender][id] = true;

        emit PackagePurchased(
            msg.sender, id, pkg.entryUSDT, totalUserTokens,
            vestTokens, immediateTokens, lpTokens, referrer, referralReward
        );
    }

    /**
     * @dev Add liquidity to PancakeSwap DEX
     * @param usdtAmount Amount of USDT to add
     * @param blocksAmount Amount of BLOCKS to add
     * @return liquidity Amount of LP tokens received
     */
    function _addLiquidity(uint256 usdtAmount, uint256 blocksAmount) internal returns (uint256 liquidity) {
        // Approve tokens for router
        usdt.approve(address(router), usdtAmount);
        shareToken.approve(address(router), blocksAmount);

        // Calculate minimum amounts with slippage tolerance
        uint256 usdtMin = (usdtAmount * (10_000 - slippageToleranceBps)) / 10_000;
        uint256 blocksMin = (blocksAmount * (10_000 - slippageToleranceBps)) / 10_000;

        // Add liquidity
        (, , liquidity) = router.addLiquidity(
            address(usdt),
            address(shareToken),
            usdtAmount,
            blocksAmount,
            usdtMin,
            blocksMin,
            address(this), // LP tokens go to contract (not distributed to users)
            block.timestamp + 300
        );
    }

    /**
     * @dev Process pending referral rewards for a referrer
     * @param referrer Address of the referrer
     * @return amountPaid Amount of BLOCKS tokens paid
     */
    function processPendingReferrals(address referrer) external onlyAdmin returns (uint256 amountPaid) {
        uint256 pendingAmount = pendingReferralRewards[referrer];
        require(pendingAmount > 0, "PackageManager: No pending referrals");

        uint256 treasuryBalance = IERC20(address(shareToken)).balanceOf(treasury);
        require(treasuryBalance >= pendingAmount, "PackageManager: Insufficient treasury balance");

        // Pay pending referrals
        IERC20(address(shareToken)).transferFrom(treasury, referrer, pendingAmount);

        // Update stats
        _userStats[referrer].totalReferralRewards += pendingAmount;

        // Clear pending amount
        pendingReferralRewards[referrer] = 0;

        return pendingAmount;
    }

    /**
     * @dev Emergency treasury top-up function
     * @param amount Amount of BLOCKS tokens to mint to treasury
     */
    function emergencyTreasuryTopUp(uint256 amount) external onlyAdmin {
        require(amount > 0, "PackageManager: Invalid amount");
        shareToken.mint(treasury, amount);
        emit InsufficientTreasuryFunds(amount, IERC20(address(shareToken)).balanceOf(treasury));
    }

    // View functions
    function getUserStats(address user) external view returns (UserStats memory) {
        return _userStats[user];
    }

    function getUserPurchases(address user) external view returns (UserPurchase[] memory) {
        return _userPurchases[user];
    }

    function getUserPurchaseCount(address user) external view returns (uint256) {
        return _userPurchases[user].length;
    }

    function getPackage(uint256 id) external view returns (Package memory) {
        require(_packages[id].exists, "PackageManager: Package does not exist");
        return _packages[id];
    }

    function getAllPackages() external view returns (Package[] memory) {
        Package[] memory packages = new Package[](_packageIds.length);
        for (uint256 i = 0; i < _packageIds.length; i++) {
            packages[i] = _packages[_packageIds[i]];
        }
        return packages;
    }

    function getActivePackages() external view returns (Package[] memory) {
        uint256 activeCount = 0;
        for (uint256 i = 0; i < _packageIds.length; i++) {
            if (_packages[_packageIds[i]].active) {
                activeCount++;
            }
        }

        Package[] memory activePackages = new Package[](activeCount);
        uint256 index = 0;
        for (uint256 i = 0; i < _packageIds.length; i++) {
            if (_packages[_packageIds[i]].active) {
                activePackages[index] = _packages[_packageIds[i]];
                index++;
            }
        }
        return activePackages;
    }

    function getPendingReferralAmount(address referrer) external view returns (uint256) {
        return pendingReferralRewards[referrer];
    }

    function getReferralEarnings(address referrer) external view returns (ReferralEarning[] memory) {
        return _referralEarnings[referrer];
    }

    function getTreasuryBalanceStatus() external view returns (uint256 currentBalance, uint256 minimumThreshold, bool isLowBalance) {
        currentBalance = IERC20(address(shareToken)).balanceOf(treasury);
        minimumThreshold = treasuryMinimumThreshold;
        isLowBalance = currentBalance < minimumThreshold;
    }

    /**
     * @dev Calculate package token splits for preview
     * @param packageId Package ID
     * @return totalTokens Total BLOCKS tokens user will receive
     * @return vestTokens BLOCKS tokens that will be vested
     * @return poolTokens BLOCKS tokens for immediate use
     * @return lpTokens BLOCKS-LP tokens user will receive
     * @return referralRewardAmount BLOCKS tokens for referral reward
     */
    function calculatePackageSplits(uint256 packageId) external view validPackage(packageId) returns (
        uint256 totalTokens,
        uint256 vestTokens,
        uint256 poolTokens,
        uint256 lpTokens,
        uint256 referralRewardAmount
    ) {
        Package storage pkg = _packages[packageId];

        // Calculate user tokens based on package exchange rate
        totalTokens = (pkg.entryUSDT * 1e18) / pkg.exchangeRate;

        // Calculate vesting allocation
        vestTokens = (totalTokens * pkg.vestBps) / 10_000;
        poolTokens = totalTokens - vestTokens;

        // Calculate LP tokens (based on 30% USDT allocation and global target price)
        if (globalTargetPrice > 0) {
            uint256 usdtForPool = (pkg.entryUSDT * (10_000 - treasuryAllocationBps)) / 10_000; // 30%
            uint256 liquidityBLOCKS = (usdtForPool * 1e18) / globalTargetPrice;
            lpTokens = liquidityBLOCKS; // 1:1 ratio
        }

        // Calculate referral reward
        referralRewardAmount = (totalTokens * pkg.referralBps) / 10_000;
    }
}
