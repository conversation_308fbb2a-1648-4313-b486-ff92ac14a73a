# BlockCoop M-Pesa Backend - Complete cPanel Deployment Guide

## 🎯 Overview
This guide will help you deploy your M-Pesa backend to cPanel hosting and connect it with your live frontend.

## 📋 Prerequisites
- ✅ cPanel hosting with Node.js support
- ✅ Domain/subdomain for API (recommended: `api.blockcoopsacco.com`)
- ✅ M-Pesa API credentials from Safaricom
- ✅ SSL certificate for your domain

## 🚀 Step-by-Step Deployment

### Step 1: Prepare Your Domain/Subdomain

1. **Create Subdomain** (Recommended approach):
   - In cPanel → Subdomains
   - Create: `api.blockcoopsacco.com`
   - Point to: `/public_html/api`

2. **Alternative**: Use main domain path
   - Use: `blockcoopsacco.com/api`
   - Deploy to: `/public_html/api`

### Step 2: Upload Backend Files

1. **Download the deployment package**:
   - File: `backend/mpesa-backend-cpanel.zip`
   - This contains all necessary files

2. **Upload to cPanel**:
   - Go to cPanel → File Manager
   - Navigate to `/public_html/api` (or your chosen directory)
   - Upload `mpesa-backend-cpanel.zip`
   - Extract the ZIP file
   - Move contents of `mpesa-backend-deploy` folder to current directory

### Step 3: Configure Node.js Application

1. **Access Node.js App Manager**:
   - Go to cPanel → Node.js App
   - Click "Create Application"

2. **Application Settings**:
   ```
   Node.js Version: 18.x or higher
   Application Mode: Production
   Application Root: /public_html/api
   Application URL: api.blockcoopsacco.com
   Application Startup File: app.js
   ```

3. **Environment Variables** (CRITICAL):
   Add these in the Node.js App environment section:
   ```
   NODE_ENV=production
   PORT=3001
   
   # M-Pesa Configuration (GET FROM SAFARICOM)
   MPESA_ENVIRONMENT=sandbox  # Change to 'production' when ready
   MPESA_CONSUMER_KEY=your_consumer_key_here
   MPESA_CONSUMER_SECRET=your_consumer_secret_here
   MPESA_BUSINESS_SHORT_CODE=your_short_code_here
   MPESA_PASSKEY=your_passkey_here
   
   # URLs
   CALLBACK_BASE_URL=https://api.blockcoopsacco.com
   CORS_ORIGIN=https://shares.blockcoopsacco.com
   
   # Security
   JWT_SECRET=your_strong_jwt_secret_minimum_32_characters
   
   # Exchange Rates
   USD_TO_KES_RATE=149.25
   KES_TO_USD_RATE=0.0067
   ```

### Step 4: Install Dependencies & Start

1. **Install Dependencies**:
   - In Node.js App interface, click "Run NPM Install"
   - Wait for completion (may take 2-3 minutes)

2. **Start Application**:
   - Click "Start App"
   - Monitor for any errors in logs

### Step 5: Test Backend Deployment

1. **Health Check**:
   - Visit: `https://api.blockcoopsacco.com/health`
   - Should return JSON with status "OK"

2. **API Status**:
   - Visit: `https://api.blockcoopsacco.com/api/status`
   - Should show API information

### Step 6: Update Frontend Configuration

1. **Rebuild Frontend**:
   ```bash
   # In your frontend directory
   npm run build
   ```

2. **Upload New Frontend**:
   - Upload new `dist` folder contents to `/public_html/shares`
   - Replace existing files

### Step 7: Configure M-Pesa Callback URLs

**In Safaricom Developer Portal**:
1. Update your app's callback URLs:
   ```
   Callback URL: https://api.blockcoopsacco.com/api/mpesa/callback
   Timeout URL: https://api.blockcoopsacco.com/api/mpesa/timeout
   ```

## 🧪 Testing Your Deployment

### 1. Test API Connection
```javascript
// Run in browser console on your live site
fetch('https://api.blockcoopsacco.com/api/status')
  .then(response => response.json())
  .then(data => console.log('API Status:', data))
  .catch(error => console.error('API Error:', error));
```

### 2. Test M-Pesa Integration
1. Try a small test payment (KES 1)
2. Monitor backend logs in cPanel
3. Check transaction is recorded

## 🔧 Troubleshooting

### Common Issues:

**1. CORS Errors**
- Verify `CORS_ORIGIN` matches your frontend domain exactly
- Check SSL certificates are working
- Ensure both domains use HTTPS

**2. Node.js App Won't Start**
- Check environment variables are set correctly
- Review error logs in cPanel Node.js interface
- Verify all dependencies installed successfully

**3. M-Pesa Callback Issues**
- Ensure callback URLs are publicly accessible
- Check SSL certificate validity
- Verify M-Pesa credentials are correct

**4. Database Issues**
- Check file permissions for `data` directory
- Ensure SQLite can write to database file

### Log Monitoring:
- Check cPanel Node.js App logs regularly
- Monitor for M-Pesa API errors
- Watch for database connection issues

## 🔒 Security Checklist

- ✅ SSL certificate installed and working
- ✅ Strong JWT secret (32+ characters)
- ✅ Environment variables properly set
- ✅ CORS configured for your domain only
- ✅ Rate limiting enabled
- ✅ M-Pesa credentials secured

## 📞 Support

If you encounter issues:
1. Check cPanel error logs first
2. Verify all environment variables
3. Test each component individually
4. Monitor M-Pesa API responses

## 🎉 Success Indicators

Your deployment is successful when:
- ✅ Health endpoint returns "OK"
- ✅ Frontend can connect to backend
- ✅ M-Pesa payments initiate successfully
- ✅ Callbacks are received and processed
- ✅ Transactions are stored in database

---

**Next Steps**: Once deployed successfully, consider:
- Setting up monitoring and alerts
- Implementing backup strategies
- Moving from sandbox to production M-Pesa
- Adding analytics and logging
