{"_format": "hh-sol-artifact-1", "contractName": "SecondaryMarket", "sourceName": "contracts/SecondaryMarket.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_usdtToken", "type": "address"}, {"internalType": "address", "name": "_blocksToken", "type": "address"}, {"internalType": "address", "name": "_router", "type": "address"}, {"internalType": "address", "name": "_factory", "type": "address"}, {"internalType": "address", "name": "_feeRecipient", "type": "address"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "uint256", "name": "_targetPrice", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "oldRecipient", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newRecipient", "type": "address"}], "name": "FeeRecipientUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "<PERSON><PERSON><PERSON>", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "new<PERSON>ee", "type": "uint256"}], "name": "SwapFeeUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newPrice", "type": "uint256"}], "name": "TargetPriceUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "blocksAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "usdtAmount", "type": "uint256"}], "name": "TokensSwapped", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "OPERATOR_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "factory", "outputs": [{"internalType": "contract IPancakeFactory", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feeRecipient", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "blocksAmount", "type": "uint256"}], "name": "getSwapQuote", "outputs": [{"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "router", "outputs": [{"internalType": "contract IPancakeRouter", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "blocksAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minUsdtAmount", "type": "uint256"}], "name": "swapBLOCKSForUSDT", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "swapFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minBlocksAmount", "type": "uint256"}], "name": "swapUSDTForBLOCKS", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "targetPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newFeeRecipient", "type": "address"}], "name": "updateFeeRecipient", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newSwapFee", "type": "uint256"}], "name": "updateSwapFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newTargetPrice", "type": "uint256"}], "name": "updateTargetPrice", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "usdtToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}