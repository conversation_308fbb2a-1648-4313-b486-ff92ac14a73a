{"_format": "hh-sol-artifact-1", "contractName": "ISwapTaxManager", "sourceName": "contracts/PackageManagerV2_Fixed.sol", "abi": [{"inputs": [{"internalType": "bytes32", "name": "key", "type": "bytes32"}], "name": "buckets", "outputs": [{"internalType": "uint16", "name": "rateBps", "type": "uint16"}, {"internalType": "address", "name": "recipient", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}