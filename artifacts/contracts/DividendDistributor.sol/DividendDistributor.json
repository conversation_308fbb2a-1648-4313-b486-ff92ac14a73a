{"_format": "hh-sol-artifact-1", "contractName": "DividendDistributor", "sourceName": "contracts/DividendDistributor.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_blocksToken", "type": "address"}, {"internalType": "address", "name": "_dividendToken", "type": "address"}, {"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "DividendClaimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalSupply", "type": "uint256"}], "name": "DividendDistributed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DISTRIBUTOR_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimDividend", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "distributeDividends", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "dividendToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getDividendStats", "outputs": [{"internalType": "uint256", "name": "totalDistributed", "type": "uint256"}, {"internalType": "uint256", "name": "totalClaimed", "type": "uint256"}, {"internalType": "uint256", "name": "totalPending", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getDividendsWithdrawn", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getPendingDividends", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getTotalDividendsEarned", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalDividendsDistributed", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "updateDividendTracker", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}