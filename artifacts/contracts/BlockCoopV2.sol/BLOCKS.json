{"_format": "hh-sol-artifact-1", "contractName": "BLOCKS", "sourceName": "contracts/BlockCoopV2.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "_swapTaxManager", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "pair", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "isAMM", "type": "bool"}], "name": "AMMStatusUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "taxKey", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "taxAmount", "type": "uint256"}], "name": "SwapTaxApplied", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldManager", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newManager", "type": "address"}], "name": "SwapTaxManagerUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "BUY_TAX_KEY", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MINTER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "SELL_TAX_KEY", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TAX_MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isAMM", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "pair", "type": "address"}, {"internalType": "bool", "name": "_isAMM", "type": "bool"}], "name": "setAMMStatus", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_swapTaxManager", "type": "address"}], "name": "setSwapTaxManager", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "swapTaxManager", "outputs": [{"internalType": "contract ISwapTaxManager", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}