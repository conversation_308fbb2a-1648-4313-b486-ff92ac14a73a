{"_format": "hh-sol-artifact-1", "contractName": "BLOCKS_LP", "sourceName": "contracts/BlockCoopV2.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "BURNER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MINTER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}