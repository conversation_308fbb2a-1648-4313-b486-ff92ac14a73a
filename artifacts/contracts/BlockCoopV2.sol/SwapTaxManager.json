{"_format": "hh-sol-artifact-1", "contractName": "SwapTaxManager", "sourceName": "contracts/BlockCoopV2.sol", "abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "key", "type": "bytes32"}, {"indexed": false, "internalType": "uint16", "name": "rateBps", "type": "uint16"}, {"indexed": false, "internalType": "address", "name": "recipient", "type": "address"}], "name": "BucketSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "buckets", "outputs": [{"internalType": "uint16", "name": "rateBps", "type": "uint16"}, {"internalType": "address", "name": "recipient", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "key", "type": "bytes32"}], "name": "getTaxBucket", "outputs": [{"components": [{"internalType": "uint16", "name": "rateBps", "type": "uint16"}, {"internalType": "address", "name": "recipient", "type": "address"}], "internalType": "struct SwapTaxManager.TaxBucket", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "key", "type": "bytes32"}, {"internalType": "uint16", "name": "rateBps", "type": "uint16"}, {"internalType": "address", "name": "recipient", "type": "address"}], "name": "setBucket", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}