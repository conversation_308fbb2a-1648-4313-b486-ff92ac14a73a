{"_format": "hh-sol-artifact-1", "contractName": "USDTTestToken", "sourceName": "contracts/BlockCoopV2.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MINTER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burnFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}