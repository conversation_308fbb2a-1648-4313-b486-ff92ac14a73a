# 🧪 BlockCoop V2 Frontend Testing Guide

## 🎯 **Testing Objectives**

We need to verify that the portfolio page now works correctly and all smart contract integrations are functioning with the correct USDT address.

## 📋 **Pre-Testing Checklist**

### ✅ **Environment Status**
- ✅ Development server running: `http://localhost:5173/`
- ✅ Correct USDT address: `******************************************`
- ✅ All contracts deployed and verified on BSC Testnet
- ✅ ABIs updated for frontend integration

### ✅ **Contract Addresses (Final)**
```
PackageManager: ******************************************
BLOCKS Token:   ******************************************
BLOCKS-LP:      ******************************************
USDT Token:     ******************************************
```

## 🔍 **Testing Sequence**

### **Phase 1: Basic Connectivity** 🌐

1. **Open Application**
   - Navigate to: `http://localhost:5173/`
   - ✅ Check: Page loads without errors
   - ✅ Check: No console errors in browser DevTools

2. **Wallet Connection**
   - Click "Connect Wallet" button
   - Connect to MetaMask with BSC Testnet
   - ✅ Check: Wallet connects successfully
   - ✅ Check: Correct network (BSC Testnet - Chain ID 97)

3. **Network Verification**
   - ✅ Check: Application detects BSC Testnet
   - ✅ Check: No network switching prompts

### **Phase 2: Portfolio Page Testing** 📊

4. **Navigate to Portfolio**
   - Click on "Portfolio" or "Dashboard" link
   - ✅ Check: Page loads without "error loading portfolio stats"
   - ✅ Check: Portfolio components render correctly

5. **Portfolio Stats Verification**
   - ✅ Check: Total Investment shows (should be 0.0 USDT for new wallet)
   - ✅ Check: BLOCKS Balance shows (should be 0.0 BLOCKS)
   - ✅ Check: LP Tokens shows (should be 0.0 BLOCKS-LP)
   - ✅ Check: Vested Tokens section appears
   - ✅ Check: No error messages or loading failures

6. **Investment Summary**
   - ✅ Check: Purchase history section loads
   - ✅ Check: "No purchases yet" or similar message for new wallet
   - ✅ Check: Referral stats section appears

### **Phase 3: Package Purchase Testing** 💰

7. **Navigate to Packages**
   - Go to packages/investment page
   - ✅ Check: Page loads correctly
   - ✅ Check: Package list appears (may be empty initially)

8. **Package Creation (Admin Function)**
   - If you have admin access, try creating a test package
   - ✅ Check: Package creation form works
   - ✅ Check: Package appears in list after creation

### **Phase 4: Trading Page Testing** 🔄

9. **Navigate to Trading**
   - Go to Trading/DEX page
   - ✅ Check: Trading interface loads
   - ✅ Check: USDT ↔ BLOCKS swap options appear
   - ✅ Check: Balance displays correctly

10. **Token Balance Verification**
    - ✅ Check: USDT balance shows correctly
    - ✅ Check: BLOCKS balance shows correctly
    - ✅ Check: Balances update when wallet changes

### **Phase 5: Contract Integration Testing** 🔗

11. **Smart Contract Calls**
    - ✅ Check: getUserStats function works (no errors)
    - ✅ Check: Token balance queries work
    - ✅ Check: Package data loads correctly
    - ✅ Check: Global target price displays

12. **Error Handling**
    - ✅ Check: Graceful handling of failed contract calls
    - ✅ Check: Appropriate error messages for users
    - ✅ Check: No infinite loading states

## 🚨 **Common Issues to Watch For**

### **Previous Issues (Should be FIXED):**
- ❌ "Error loading portfolio stats" → ✅ Should be resolved
- ❌ Contract address mismatches → ✅ Should be resolved
- ❌ Wrong USDT token → ✅ Should be resolved
- ❌ RPC connection failures → ✅ Should be resolved

### **New Issues to Monitor:**
- 🔍 Slow loading times
- 🔍 Console errors or warnings
- 🔍 Incorrect balance displays
- 🔍 Failed contract interactions

## 📝 **Testing Results Template**

### **✅ PASS / ❌ FAIL - Test Results**

**Basic Connectivity:**
- [ ] Application loads
- [ ] Wallet connects
- [ ] Network detection

**Portfolio Page:**
- [ ] Portfolio stats load
- [ ] No error messages
- [ ] Investment summary works

**Package System:**
- [ ] Package page loads
- [ ] Package data displays

**Trading Interface:**
- [ ] Trading page loads
- [ ] Balance displays
- [ ] Swap interface works

**Contract Integration:**
- [ ] Smart contract calls succeed
- [ ] Error handling works
- [ ] Data updates correctly

## 🎯 **Success Criteria**

### **Primary Goals:**
1. ✅ **Portfolio page loads without errors**
2. ✅ **All contract addresses resolve correctly**
3. ✅ **User stats and balances display properly**
4. ✅ **No "error loading portfolio stats" message**

### **Secondary Goals:**
1. ✅ **Package purchase flow works**
2. ✅ **Trading interface functional**
3. ✅ **Referral system displays correctly**
4. ✅ **Vesting information shows properly**

## 🔧 **If Issues Found**

### **Debugging Steps:**
1. **Check Browser Console** - Look for JavaScript errors
2. **Check Network Tab** - Verify API calls and responses
3. **Verify Contract Addresses** - Ensure .env variables are correct
4. **Test Contract Calls** - Use browser console to test direct calls
5. **Check RPC Connection** - Verify BSC Testnet connectivity

### **Quick Fixes:**
- **Clear browser cache** and reload
- **Disconnect and reconnect** wallet
- **Switch networks** and switch back
- **Restart development server** if needed

## 🚀 **Ready to Test!**

The application is now running at `http://localhost:5173/` with all the fixes implemented. Please go through the testing sequence and let me know what you find!

**Expected Result:** Portfolio page should now work perfectly without any "error loading portfolio stats" messages.
