import { ethers } from 'ethers';
import clsx, { ClassValue } from 'clsx';

export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

export function formatEther(value: bigint | string, decimals = 4): string {
  try {
    const formatted = ethers.formatEther(value);
    const num = parseFloat(formatted);
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals,
    });
  } catch {
    return '0';
  }
}

// Detect if a value is stored in 6-decimal or 18-decimal format
function detectUSDTDecimals(value: bigint): number {
  // Convert to number for analysis
  const numValue = Number(value);

  // If the value is very small (< 1e12), it's likely 6-decimal data being interpreted as 18-decimal
  // For example: 100 USDT stored as 100000000 (6-decimal) would be 0.0000000001 when formatted as 18-decimal
  if (numValue > 0 && numValue < 1e12) {
    return 6; // Legacy 6-decimal format
  }

  return 18; // V2 18-decimal format
}

export function formatUSDT(value: bigint | string): string {
  try {
    const bigintValue = typeof value === 'string' ? BigInt(value) : value;

    // Auto-detect the decimal precision based on the value magnitude
    const decimals = detectUSDTDecimals(bigintValue);

    const formatted = ethers.formatUnits(bigintValue, decimals);
    const num = parseFloat(formatted);
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    });
  } catch {
    return '0';
  }
}

// Detect if an exchange rate is stored in 6-decimal or 18-decimal format
function detectExchangeRateDecimals(value: bigint): number {
  const numValue = Number(value);

  // Exchange rates in 6-decimal format are typically small numbers (e.g., 7000 = 0.007 USDT/BLOCKS)
  // Exchange rates in 18-decimal format would be large numbers (e.g., 7000000000000000 = 0.007 USDT/BLOCKS)
  if (numValue > 0 && numValue < 1e12) {
    return 6; // Legacy 6-decimal format
  }

  return 18; // V2 18-decimal format
}

export function formatExchangeRate(value: bigint | string): string {
  try {
    const bigintValue = typeof value === 'string' ? BigInt(value) : value;

    // Auto-detect the decimal precision
    const decimals = detectExchangeRateDecimals(bigintValue);

    const formatted = ethers.formatUnits(bigintValue, decimals);
    const num = parseFloat(formatted);
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 6,
    });
  } catch {
    return '0';
  }
}

export function formatBLOCKS(value: bigint | string): string {
  try {
    const bigintValue = typeof value === 'string' ? BigInt(value) : value;
    // BLOCKS tokens always use 18 decimals
    const formatted = ethers.formatUnits(bigintValue, 18);
    const num = parseFloat(formatted);
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 4,
    });
  } catch {
    return '0';
  }
}

export function formatAddress(address: string): string {
  if (!address) return '';
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

export function formatDuration(seconds: number): string {
  const years = Math.floor(seconds / (365 * 24 * 3600));
  const months = Math.floor((seconds % (365 * 24 * 3600)) / (30 * 24 * 3600));
  
  if (years > 0) {
    return months > 0 ? `${years}y ${months}m` : `${years}y`;
  }
  return months > 0 ? `${months}m` : '< 1m';
}

export function formatPercentage(bps: number): string {
  return `${(bps / 100).toFixed(1)}%`;
}

export function parseEther(value: string): bigint {
  try {
    return ethers.parseEther(value || '0');
  } catch {
    return 0n;
  }
}

export function shortenAddress(address: string): string {
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}