// Test USDT formatting with 18 decimals
const { ethers } = require('ethers');

console.log('🔍 Testing USDT Formatting (18 decimals)');
console.log('=' .repeat(50));

// Test cases for USDT amounts
const testCases = [
  {
    name: 'Package Purchase - 100 USDT',
    rawValue: '100000000000000000000', // 100 * 10^18
    expected: '100.00'
  },
  {
    name: 'Package Purchase - 500 USDT', 
    rawValue: '500000000000000000000', // 500 * 10^18
    expected: '500.00'
  },
  {
    name: 'Package Purchase - 1000 USDT',
    rawValue: '1000000000000000000000', // 1000 * 10^18
    expected: '1000.00'
  },
  {
    name: 'Small Amount - 0.01 USDT',
    rawValue: '10000000000000000', // 0.01 * 10^18
    expected: '0.01'
  },
  {
    name: 'Large Amount - 10000 USDT',
    rawValue: '10000000000000000000000', // 10000 * 10^18
    expected: '10000.00'
  }
];

// Simulate the formatTokenAmount function from frontend
function formatTokenAmount(amount, decimals = 18, displayDecimals = 2) {
  if (amount === null || amount === undefined) {
    return '0.00';
  }

  try {
    return parseFloat(ethers.formatUnits(amount, decimals)).toFixed(displayDecimals);
  } catch (error) {
    console.warn('Error formatting token amount:', error, 'amount:', amount);
    return '0.00';
  }
}

console.log('📊 Testing formatTokenAmount with 18 decimals:');
console.log('-'.repeat(50));

let allPassed = true;

for (const testCase of testCases) {
  const result = formatTokenAmount(testCase.rawValue, 18, 2);
  const passed = result === testCase.expected;
  
  console.log(`${passed ? '✅' : '❌'} ${testCase.name}`);
  console.log(`   Raw Value: ${testCase.rawValue}`);
  console.log(`   Expected: ${testCase.expected} USDT`);
  console.log(`   Got: ${result} USDT`);
  console.log(`   Status: ${passed ? 'PASS' : 'FAIL'}`);
  console.log('');
  
  if (!passed) {
    allPassed = false;
  }
}

console.log('🎯 Overall Result:');
console.log(`${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

if (allPassed) {
  console.log('🚀 USDT formatting is working correctly with 18 decimals!');
  console.log('💡 The portfolio page should now display correct amounts.');
} else {
  console.log('⚠️  There are issues with USDT formatting.');
}

// Test the problematic value from the user's report
console.log('\n🔍 Testing the problematic value:');
console.log('-'.repeat(50));

const problematicValue = '100000000000000000000'; // This should be 100 USDT
const correctResult = formatTokenAmount(problematicValue, 18, 2);
const wrongResult = formatTokenAmount(problematicValue, 6, 2); // What was happening before

console.log(`Raw Value: ${problematicValue}`);
console.log(`With 18 decimals (CORRECT): ${correctResult} USDT`);
console.log(`With 6 decimals (WRONG): ${wrongResult} USDT`);
console.log(`Fix Applied: ${correctResult === '100.00' ? '✅ YES' : '❌ NO'}`);
