{"timestamp": "2025-07-28T14:14:31.101Z", "network": "hardhat", "deployer": "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266", "version": "v2-18-decimal-usdt", "features": ["18-decimal USDT test token for V2 architecture", "Updated decimal handling in PackageManager", "Consistent 18-decimal precision across all tokens", "Enhanced compatibility with V2 modular architecture"], "contracts": {"USDTTestToken": "0x5FbDB2315678afecb367f032d93F642f64180aa3", "PackageManagerV2_1": "0x5FC8d32690cc91D4c39d9d3abcBD16989F875707", "BLOCKS": "0x9fE46736679d2D9a65F0992F2272dE9f3c7fa6e0", "BLOCKS_LP": "0xCf7Ed3AccA5a467e9e704C703E8D87F634fB0Fc9", "VestingVault": "0xDc64a140Aa3E981100a9becA4E685f962f0cF6C9", "SwapTaxManager": "0xe7f1725E7734CE288F8367e1Bb143E90bb3F0512", "PancakeRouter": "0xD99D1c33F9fC3444f8101754aBC46c52416550D1", "Treasury": "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266"}, "pricing": {"globalTargetPrice": "2.0", "testPackages": [{"name": "Starter Package", "exchangeRate": "1.5"}, {"name": "Growth Package", "exchangeRate": "1.8"}, {"name": "Premium Package", "exchangeRate": "2.2"}]}, "gasUsed": {"USDTTestToken": "892804", "BLOCKS": "1108520", "BLOCKS_LP": "837602", "VestingVault": "635612", "SwapTaxManager": "430310", "PackageManagerV2_1": "4032151"}}