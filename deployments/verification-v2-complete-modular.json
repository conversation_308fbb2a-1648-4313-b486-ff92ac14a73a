{"timestamp": "2025-07-28T19:36:02.039Z", "network": "bsctestnet", "totalContracts": 8, "verified": 0, "failed": 8, "results": {"USDTTestToken": "FAILED: The selected network is \"hardhat\", which is not supported for contract verification.\n\nIf you intended to use a different network, ensure that you provide the --network parameter when running the command.\n\nFor example: npx hardhat verify --network <network-name>", "SwapTaxManager": "FAILED: The selected network is \"hardhat\", which is not supported for contract verification.\n\nIf you intended to use a different network, ensure that you provide the --network parameter when running the command.\n\nFor example: npx hardhat verify --network <network-name>", "BLOCKS": "FAILED: The selected network is \"hardhat\", which is not supported for contract verification.\n\nIf you intended to use a different network, ensure that you provide the --network parameter when running the command.\n\nFor example: npx hardhat verify --network <network-name>", "BLOCKS-LP": "FAILED: The selected network is \"hardhat\", which is not supported for contract verification.\n\nIf you intended to use a different network, ensure that you provide the --network parameter when running the command.\n\nFor example: npx hardhat verify --network <network-name>", "VestingVault": "FAILED: The selected network is \"hardhat\", which is not supported for contract verification.\n\nIf you intended to use a different network, ensure that you provide the --network parameter when running the command.\n\nFor example: npx hardhat verify --network <network-name>", "PackageManagerV2_1": "FAILED: Cannot read properties of undefined (reading 'parseUnits')", "DividendDistributor": "FAILED: The selected network is \"hardhat\", which is not supported for contract verification.\n\nIf you intended to use a different network, ensure that you provide the --network parameter when running the command.\n\nFor example: npx hardhat verify --network <network-name>", "SecondaryMarket": "FAILED: Cannot read properties of undefined (reading 'parseUnits')"}}