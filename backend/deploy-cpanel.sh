#!/bin/bash

# BlockCoop M-Pesa Backend - cPanel Deployment Script
# This script prepares the backend for cPanel Node.js deployment

echo "🚀 Preparing BlockCoop M-Pesa Backend for cPanel deployment..."

# Create deployment directory
DEPLOY_DIR="mpesa-backend-deploy"
rm -rf $DEPLOY_DIR
mkdir -p $DEPLOY_DIR

echo "📁 Creating deployment package..."

# Copy essential files
cp -r src $DEPLOY_DIR/
cp package.json $DEPLOY_DIR/
cp package-lock.json $DEPLOY_DIR/ 2>/dev/null || echo "No package-lock.json found"
cp .env.production $DEPLOY_DIR/.env
cp README.md $DEPLOY_DIR/ 2>/dev/null || echo "No README.md found"

# Create necessary directories
mkdir -p $DEPLOY_DIR/data
mkdir -p $DEPLOY_DIR/logs

# Create startup script for cPanel
cat > $DEPLOY_DIR/app.js << 'EOF'
// cPanel Node.js Entry Point
// This file is required by cPanel Node.js applications

import('./src/server.js')
  .then(() => {
    console.log('BlockCoop M-Pesa Backend started successfully');
  })
  .catch((error) => {
    console.error('Failed to start server:', error);
    process.exit(1);
  });
EOF

# Create cPanel-specific package.json
cat > $DEPLOY_DIR/package.json << 'EOF'
{
  "name": "blockcoop-mpesa-backend",
  "version": "1.0.0",
  "description": "Backend API for M-Pesa payment integration with BlockCoop Sacco",
  "main": "app.js",
  "type": "module",
  "scripts": {
    "start": "node app.js",
    "dev": "node app.js"
  },
  "dependencies": {
    "axios": "^1.6.0",
    "bcryptjs": "^2.4.3",
    "better-sqlite3": "^12.2.0",
    "compression": "^1.7.4",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "ethers": "^6.8.1",
    "express": "^4.18.2",
    "express-rate-limit": "^7.1.5",
    "express-validator": "^7.0.1",
    "helmet": "^7.1.0",
    "jsonwebtoken": "^9.0.2",
    "morgan": "^1.10.0",
    "sqlite3": "^5.1.6",
    "uuid": "^9.0.1",
    "winston": "^3.11.0"
  },
  "keywords": [
    "mpesa",
    "blockchain",
    "usdt",
    "payment",
    "api",
    "nodejs",
    "express"
  ],
  "author": "BlockCoop Sacco",
  "license": "MIT"
}
EOF

# Create deployment instructions
cat > $DEPLOY_DIR/DEPLOYMENT_INSTRUCTIONS.md << 'EOF'
# BlockCoop M-Pesa Backend - cPanel Deployment Instructions

## Prerequisites
- cPanel hosting with Node.js support
- Domain/subdomain for API (e.g., api.blockcoopsacco.com)
- M-Pesa API credentials from Safaricom

## Deployment Steps

### 1. Upload Files
1. Compress the entire deployment folder into a ZIP file
2. Upload to your cPanel File Manager
3. Extract in the public_html directory (or subdomain directory)

### 2. Configure Node.js Application
1. Go to cPanel → Node.js App
2. Click "Create Application"
3. Set Node.js version: 18.x or higher
4. Application root: /public_html/api (or your chosen path)
5. Application URL: api.blockcoopsacco.com
6. Application startup file: app.js

### 3. Environment Variables
In cPanel Node.js App, add these environment variables:
- Copy all variables from .env file
- Update CALLBACK_BASE_URL to your domain
- Update CORS_ORIGIN to your frontend domain
- Add your M-Pesa production credentials

### 4. Install Dependencies
1. In cPanel Node.js App, click "Run NPM Install"
2. Wait for installation to complete

### 5. Start Application
1. Click "Start App" in cPanel Node.js interface
2. Monitor logs for any errors

### 6. Test Deployment
- Visit: https://api.blockcoopsacco.com/health
- Should return: {"status":"OK","timestamp":"..."}

## Important Notes
- Ensure your domain has SSL certificate
- Update frontend API URL to point to new backend
- Test M-Pesa integration thoroughly
- Monitor logs for any issues

## Troubleshooting
- Check Node.js app logs in cPanel
- Verify environment variables are set correctly
- Ensure all dependencies are installed
- Check file permissions if needed
EOF

# Create .htaccess for proper routing (if needed)
cat > $DEPLOY_DIR/.htaccess << 'EOF'
# Redirect all requests to Node.js app
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ app.js [L]
EOF

echo "✅ Deployment package created in '$DEPLOY_DIR' directory"
echo ""
echo "📋 Next steps:"
echo "1. Compress the '$DEPLOY_DIR' folder into a ZIP file"
echo "2. Upload to your cPanel File Manager"
echo "3. Follow the instructions in DEPLOYMENT_INSTRUCTIONS.md"
echo ""
echo "🔧 Don't forget to:"
echo "- Update environment variables with production values"
echo "- Configure your domain/subdomain"
echo "- Update frontend API URL"
echo ""
echo "🚀 Ready for cPanel deployment!"
