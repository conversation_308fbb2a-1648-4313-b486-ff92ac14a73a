# BlockCoop M-Pesa Backend - cPanel Deployment Instructions

## Prerequisites
- cPanel hosting with Node.js support
- Domain/subdomain for API (e.g., api.blockcoopsacco.com)
- M-Pesa API credentials from Safaricom

## Deployment Steps

### 1. Upload Files
1. Compress the entire deployment folder into a ZIP file
2. Upload to your cPanel File Manager
3. Extract in the public_html directory (or subdomain directory)

### 2. Configure Node.js Application
1. Go to cPanel → Node.js App
2. Click "Create Application"
3. Set Node.js version: 18.x or higher
4. Application root: /public_html/api (or your chosen path)
5. Application URL: api.blockcoopsacco.com
6. Application startup file: app.js

### 3. Environment Variables
In cPanel Node.js App, add these environment variables:
- Copy all variables from .env file
- Update CALLBACK_BASE_URL to your domain
- Update CORS_ORIGIN to your frontend domain
- Add your M-Pesa production credentials

### 4. Install Dependencies
1. In cPanel Node.js App, click "Run NPM Install"
2. Wait for installation to complete

### 5. Start Application
1. Click "Start App" in cPanel Node.js interface
2. Monitor logs for any errors

### 6. Test Deployment
- Visit: https://api.blockcoopsacco.com/health
- Should return: {"status":"OK","timestamp":"..."}

## Important Notes
- Ensure your domain has SSL certificate
- Update frontend API URL to point to new backend
- Test M-Pesa integration thoroughly
- Monitor logs for any issues

## Troubleshooting
- Check Node.js app logs in cPanel
- Verify environment variables are set correctly
- Ensure all dependencies are installed
- Check file permissions if needed
