{"_format": "hh-sol-artifact-1", "contractName": "BlockCoopTestTether", "sourceName": "contracts/src/BlockCoopTestTether.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "logoURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x608060405234801561001057600080fd5b50600436106101005760003560e01c806370a0823111610097578063a457c2d711610066578063a457c2d7146101fe578063a9059cbb14610211578063dd62ed3e14610224578063f2fde38b1461023757600080fd5b806370a08231146101aa578063715018a6146101d35780638da5cb5b146101db57806395d89b41146101f657600080fd5b8063313ce567116100d3578063313ce5671461016b578063395093511461017a57806340c10f191461018d5780636bb38b28146101a257600080fd5b806306fdde0314610105578063095ea7b31461012357806318160ddd1461014657806323b872dd14610158575b600080fd5b61010d61024a565b60405161011a91906109e2565b60405180910390f35b610136610131366004610a4c565b6102dc565b604051901515815260200161011a565b6002545b60405190815260200161011a565b610136610166366004610a76565b6102f6565b6040516006815260200161011a565b610136610188366004610a4c565b61031a565b6101a061019b366004610a4c565b61033c565b005b61010d610352565b61014a6101b8366004610ab2565b6001600160a01b031660009081526020819052604090205490565b6101a06103e0565b6005546040516001600160a01b03909116815260200161011a565b61010d6103f4565b61013661020c366004610a4c565b610403565b61013661021f366004610a4c565b610483565b61014a610232366004610ad4565b610491565b6101a0610245366004610ab2565b6104bc565b60606003805461025990610b07565b80601f016020809104026020016040519081016040528092919081815260200182805461028590610b07565b80156102d25780601f106102a7576101008083540402835291602001916102d2565b820191906000526020600020905b8154815290600101906020018083116102b557829003601f168201915b5050505050905090565b6000336102ea818585610535565b60019150505b92915050565b600033610304858285610659565b61030f8585856106d3565b506001949350505050565b6000336102ea81858561032d8383610491565b6103379190610b41565b610535565b610344610877565b61034e82826108d1565b5050565b6006805461035f90610b07565b80601f016020809104026020016040519081016040528092919081815260200182805461038b90610b07565b80156103d85780601f106103ad576101008083540402835291602001916103d8565b820191906000526020600020905b8154815290600101906020018083116103bb57829003601f168201915b505050505081565b6103e8610877565b6103f26000610990565b565b60606004805461025990610b07565b600033816104118286610491565b9050838110156104765760405162461bcd60e51b815260206004820152602560248201527f45524332303a2064656372656173656420616c6c6f77616e63652062656c6f77604482015264207a65726f60d81b60648201526084015b60405180910390fd5b61030f8286868403610535565b6000336102ea8185856106d3565b6001600160a01b03918216600090815260016020908152604080832093909416825291909152205490565b6104c4610877565b6001600160a01b0381166105295760405162461bcd60e51b815260206004820152602660248201527f4f776e61626c653a206e6577206f776e657220697320746865207a65726f206160448201526564647265737360d01b606482015260840161046d565b61053281610990565b50565b6001600160a01b0383166105975760405162461bcd60e51b8152602060048201526024808201527f45524332303a20617070726f76652066726f6d20746865207a65726f206164646044820152637265737360e01b606482015260840161046d565b6001600160a01b0382166105f85760405162461bcd60e51b815260206004820152602260248201527f45524332303a20617070726f766520746f20746865207a65726f206164647265604482015261737360f01b606482015260840161046d565b6001600160a01b0383811660008181526001602090815260408083209487168084529482529182902085905590518481527f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925910160405180910390a3505050565b60006106658484610491565b905060001981146106cd57818110156106c05760405162461bcd60e51b815260206004820152601d60248201527f45524332303a20696e73756666696369656e7420616c6c6f77616e6365000000604482015260640161046d565b6106cd8484848403610535565b50505050565b6001600160a01b0383166107375760405162461bcd60e51b815260206004820152602560248201527f45524332303a207472616e736665722066726f6d20746865207a65726f206164604482015264647265737360d81b606482015260840161046d565b6001600160a01b0382166107995760405162461bcd60e51b815260206004820152602360248201527f45524332303a207472616e7366657220746f20746865207a65726f206164647260448201526265737360e81b606482015260840161046d565b6001600160a01b038316600090815260208190526040902054818110156108115760405162461bcd60e51b815260206004820152602660248201527f45524332303a207472616e7366657220616d6f756e7420657863656564732062604482015265616c616e636560d01b606482015260840161046d565b6001600160a01b03848116600081815260208181526040808320878703905593871680835291849020805487019055925185815290927fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef910160405180910390a36106cd565b6005546001600160a01b031633146103f25760405162461bcd60e51b815260206004820181905260248201527f4f776e61626c653a2063616c6c6572206973206e6f7420746865206f776e6572604482015260640161046d565b6001600160a01b0382166109275760405162461bcd60e51b815260206004820152601f60248201527f45524332303a206d696e7420746f20746865207a65726f206164647265737300604482015260640161046d565b80600260008282546109399190610b41565b90915550506001600160a01b038216600081815260208181526040808320805486019055518481527fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef910160405180910390a35050565b600580546001600160a01b038381166001600160a01b0319831681179093556040519116919082907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e090600090a35050565b600060208083528351808285015260005b81811015610a0f578581018301518582016040015282016109f3565b506000604082860101526040601f19601f8301168501019250505092915050565b80356001600160a01b0381168114610a4757600080fd5b919050565b60008060408385031215610a5f57600080fd5b610a6883610a30565b946020939093013593505050565b600080600060608486031215610a8b57600080fd5b610a9484610a30565b9250610aa260208501610a30565b9150604084013590509250925092565b600060208284031215610ac457600080fd5b610acd82610a30565b9392505050565b60008060408385031215610ae757600080fd5b610af083610a30565b9150610afe60208401610a30565b90509250929050565b600181811c90821680610b1b57607f821691505b602082108103610b3b57634e487b7160e01b600052602260045260246000fd5b50919050565b808201808211156102f057634e487b7160e01b600052601160045260246000fdfea2646970667358221220950c0cdce11c6d59e69715d62a6c96b2de3eba8c4f1e218effdc7677441d362864736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}