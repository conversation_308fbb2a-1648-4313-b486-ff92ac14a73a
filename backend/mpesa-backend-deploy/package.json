{"name": "blockcoop-mpesa-backend", "version": "1.0.0", "description": "Backend API for M-Pesa payment integration with BlockCoop Sacco", "main": "app.js", "type": "module", "scripts": {"start": "node app.js", "dev": "node app.js"}, "dependencies": {"axios": "^1.6.0", "bcryptjs": "^2.4.3", "better-sqlite3": "^12.2.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "ethers": "^6.8.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "sqlite3": "^5.1.6", "uuid": "^9.0.1", "winston": "^3.11.0"}, "keywords": ["mpesa", "blockchain", "usdt", "payment", "api", "nodejs", "express"], "author": "BlockCoop Sacco", "license": "MIT"}