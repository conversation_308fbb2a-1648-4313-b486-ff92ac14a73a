# BlockCoop Sacco Environment Configuration
# Copy this file to .env and fill in your actual values

# Blockchain Configuration
VITE_CHAIN_ID=97
VITE_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
BSC_TESTNET_RPC=https://bsc-testnet.public.blastapi.io

# Contract Addresses (BSC Testnet)
VITE_USDT_ADDRESS=0x350eBe9e8030B5C2e70f831b82b92E44569736fF
VITE_SHARE_ADDRESS=0x715b2A06Ac3AB8506f8fF44Fa67C3D2ec3425706
VITE_LP_ADDRESS=0xB6F555cC1A5f667a6965B637302FdEC1af65DA2B
VITE_VAULT_ADDRESS=******************************************
VITE_TAX_ADDRESS=******************************************
VITE_PACKAGE_MANAGER_ADDRESS=******************************************

# PancakeSwap V2 BSC Testnet Addresses
VITE_ROUTER_ADDRESS=******************************************
VITE_FACTORY_ADDRESS=******************************************

# API Keys and Secrets (NEVER commit real values)
ADMIN_PRIVATE_KEY=your_admin_private_key_here
PRIVATE_KEY=your_private_key_here
ETHERSCAN_API_KEY=your_etherscan_api_key_here
VITE_WALLETCONNECT_PROJECT_ID=your_walletconnect_project_id_here

# M-Pesa API Configuration
VITE_MPESA_API_URL=http://localhost:3001/api
VITE_DIVIDEND_DISTRIBUTOR_ADDRESS=******************************************
VITE_SECONDARY_MARKET_ADDRESS=******************************************