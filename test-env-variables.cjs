// Quick test to verify environment variables are being read correctly
require('dotenv').config();

console.log('🔍 Testing Environment Variables');
console.log('=' .repeat(50));

console.log('📋 Contract Addresses from Environment:');
console.log('USDT:', process.env.VITE_USDT_ADDRESS);
console.log('BLOCKS:', process.env.VITE_SHARE_ADDRESS);
console.log('BLOCKS-LP:', process.env.VITE_LP_ADDRESS);
console.log('VestingVault:', process.env.VITE_VAULT_ADDRESS);
console.log('SwapTaxManager:', process.env.VITE_TAX_ADDRESS);
console.log('PackageManager:', process.env.VITE_PACKAGE_MANAGER_ADDRESS);
console.log('DividendDistributor:', process.env.VITE_DIVIDEND_DISTRIBUTOR_ADDRESS);
console.log('SecondaryMarket:', process.env.VITE_SECONDARY_MARKET_ADDRESS);

console.log('\n🎯 Expected Addresses (from deployment):');
console.log('USDT: 0x52f8BE86c4157eF5F11f3d73135ec4a568B02b90');
console.log('BLOCKS: 0x1d1669EF234081330a78Da546F1aE744e85b551F');
console.log('BLOCKS-LP: 0x9D08A478B90F84f0dF6867E0B210547E9311724F');
console.log('VestingVault: 0x21CE67C04b799183c1A88342947AD6D3b4f32430');
console.log('SwapTaxManager: 0x8188E20075Fd048b7850436eDf624b7169c53237');
console.log('PackageManager: 0xF7075036dBd8d393B4DcF63071C3eF4abD8f31b9');
console.log('DividendDistributor: 0x19C41131bE15234b6D6e8eaB2786Fc52389a142b');
console.log('SecondaryMarket: 0x04a107adDD22a92Cdbd1B3D783Cc57bAb45242f4');

console.log('\n✅ Verification:');
const expectedAddresses = {
  'VITE_USDT_ADDRESS': '0x52f8BE86c4157eF5F11f3d73135ec4a568B02b90',
  'VITE_SHARE_ADDRESS': '0x1d1669EF234081330a78Da546F1aE744e85b551F',
  'VITE_LP_ADDRESS': '0x9D08A478B90F84f0dF6867E0B210547E9311724F',
  'VITE_VAULT_ADDRESS': '0x21CE67C04b799183c1A88342947AD6D3b4f32430',
  'VITE_TAX_ADDRESS': '0x8188E20075Fd048b7850436eDf624b7169c53237',
  'VITE_PACKAGE_MANAGER_ADDRESS': '0xF7075036dBd8d393B4DcF63071C3eF4abD8f31b9',
  'VITE_DIVIDEND_DISTRIBUTOR_ADDRESS': '0x19C41131bE15234b6D6e8eaB2786Fc52389a142b',
  'VITE_SECONDARY_MARKET_ADDRESS': '0x04a107adDD22a92Cdbd1B3D783Cc57bAb45242f4'
};

let allCorrect = true;
for (const [envVar, expectedAddr] of Object.entries(expectedAddresses)) {
  const actualAddr = process.env[envVar];
  const isCorrect = actualAddr === expectedAddr;
  console.log(`${isCorrect ? '✅' : '❌'} ${envVar}: ${isCorrect ? 'CORRECT' : 'MISMATCH'}`);
  if (!isCorrect) {
    console.log(`   Expected: ${expectedAddr}`);
    console.log(`   Actual: ${actualAddr}`);
    allCorrect = false;
  }
}

console.log(`\n🎯 Overall Status: ${allCorrect ? '✅ ALL CORRECT' : '❌ ISSUES FOUND'}`);

if (allCorrect) {
  console.log('🚀 Environment variables are correctly configured!');
  console.log('🔄 Please refresh your browser to pick up the new contract addresses.');
} else {
  console.log('⚠️  Please check your .env.local file and restart the development server.');
}
